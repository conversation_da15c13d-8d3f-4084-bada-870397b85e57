// 全局变量存储CodeMirror编辑器实例
let headerEditor, userRulesEditor, existingRulesEditor;

// 防抖函数，避免频繁调用
const debounce = (func, delay) => {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 获取DOM元素
    const addSubscriptionBtn = document.getElementById('add-subscription');
    const subscriptionList = document.getElementById('subscription-list');
    const fetchNodesBtn = document.getElementById('fetch-nodes-btn');
    const nodesResult = document.getElementById('nodes-result');
    const nodesResultContent = document.getElementById('nodes-result-content');

    const saveHeaderBtn = document.getElementById('save-header-btn');
    const headerContent = document.getElementById('header-content');

    const saveRulesBtn = document.getElementById('save-rules-btn');
    const userRules = document.getElementById('user-rules');
    const existingRules = document.getElementById('existing-rules');

    // 自定义节点相关元素
    const customNodesList = document.getElementById('custom-nodes-list');
    const addCustomNodeDropdown = document.getElementById('add-custom-node-dropdown');
    const customNodesResult = document.getElementById('custom-nodes-result');
    const customNodesResultContent = document.getElementById('custom-nodes-result-content');

    const generateConfigBtn = document.getElementById('generate-config-btn');
    const configUrlContainer = document.getElementById('config-url-container');
    const configUrlInput = document.getElementById('config-url');
    const copyConfigUrlBtn = document.getElementById('copy-config-url-btn');

    const loadingOverlay = document.getElementById('loading-overlay');
    const statusContainer = document.getElementById('status-container');
    const statusMessage = document.getElementById('status-message');

    // 定时任务相关元素（稍后获取）
    let autoTaskEnabled, autoTaskTime, autoTaskStatus, nextExecutionTime, lastExecutionTime, saveAutoTaskBtn, runTaskNowBtn, configGenerationTime;

    // 初始化CodeMirror编辑器
    initCodeMirrorEditors();

    // 初始化本地存储
    initLocalStorage();

    // 事件监听器 - 添加订阅
    addSubscriptionBtn.addEventListener('click', () => {
        addSubscriptionRow();
        saveSubscriptions(); // 保存更改
    });

    // 事件监听器 - 移除订阅
    subscriptionList.addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-subscription')) {
            const row = e.target.closest('.subscription-row');
            if (subscriptionList.querySelectorAll('.subscription-row').length > 1) {
                row.remove();
                saveSubscriptions(); // 保存更改
            } else {
                showStatus('至少保留一个订阅行', 'warning');
            }
        }
    });

    // 事件监听器 - 获取节点
    fetchNodesBtn.addEventListener('click', async () => {
        await fetchNodes();
    });

    // 事件监听器 - 保存头部配置
    saveHeaderBtn.addEventListener('click', async () => {
        // 保存所有配置到服务器
        const success = await saveAllConfig();
        if (success) {
            showStatus('头部配置已保存', 'success');
        }
    });

    // 事件监听器 - 保存规则配置
    saveRulesBtn.addEventListener('click', async () => {
        // 保存所有配置到服务器
        const success = await saveAllConfig();
        if (success) {
            showStatus('规则配置已保存', 'success');
        }
    });

    // 事件监听器 - 生成配置
    generateConfigBtn.addEventListener('click', async () => {
        await generateConfig();
    });

    // 事件监听器 - 复制配置链接
    copyConfigUrlBtn.addEventListener('click', () => {
        copyToClipboard(configUrlInput.value);
    });

    // 事件监听器 - 监听标签页切换，刷新编辑器
    document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', event => {
            // 当切换到标签页时强制刷新CodeMirror编辑器
            if (headerEditor) headerEditor.refresh();
            if (userRulesEditor) userRulesEditor.refresh();
            if (existingRulesEditor) existingRulesEditor.refresh();
        });
    });

    // 事件监听器 - 监听订阅列表变化
    subscriptionList.addEventListener('input', (e) => {
        if (e.target.classList.contains('subscription-url')) {
            saveSubscriptions();
        }
    });

    // 事件监听器 - 监听订阅启用状态变化
    subscriptionList.addEventListener('change', (e) => {
        if (e.target.classList.contains('subscription-enabled')) {
            saveSubscriptions();
        }
    });

    // 事件监听器 - 自定义节点下拉菜单
    document.addEventListener('click', (e) => {
        if (e.target.closest('.dropdown-item[data-node-type]')) {
            e.preventDefault();
            const nodeType = e.target.closest('.dropdown-item').getAttribute('data-node-type');
            addCustomNode(nodeType);
        }
    });

    // 事件监听器 - 删除自定义节点
    customNodesList.addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-custom-node')) {
            const nodeCard = e.target.closest('.custom-node-card');
            nodeCard.remove();
            saveCustomNodes();
            updateCustomNodesResult();
        }
    });

    // 事件监听器 - 自定义节点输入变化
    customNodesList.addEventListener('input', (e) => {
        if (e.target.closest('.custom-node-card')) {
            saveCustomNodes();
        }
    });

    // 从本地存储加载数据
    loadFromLocalStorage();

    // 初始化定时任务功能
    initAutoTaskFeature();
});

// 初始化CodeMirror编辑器
function initCodeMirrorEditors() {
    // 配置头部编辑器
    headerEditor = CodeMirror.fromTextArea(document.getElementById('header-content'), {
        mode: 'yaml',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true,
        styleActiveLine: true,
        indentUnit: 2,
        tabSize: 2,
        autofocus: false,
        scrollbarStyle: 'simple', // 使用SimpleScrollbars插件
        viewportMargin: 10, // 设置合理的视口边距
        extraKeys: {"Tab": function(cm) {
            if (cm.somethingSelected()) {
                cm.indentSelection("add");
            } else {
                cm.replaceSelection("  ", "end");
            }
        }}
    });

    // 配置用户自定义规则编辑器
    userRulesEditor = CodeMirror.fromTextArea(document.getElementById('user-rules'), {
        mode: 'yaml',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true,
        styleActiveLine: true,
        indentUnit: 2,
        tabSize: 2,
        autofocus: false,
        scrollbarStyle: 'simple', // 使用SimpleScrollbars插件
        viewportMargin: 10, // 设置合理的视口边距
        extraKeys: {"Tab": function(cm) {
            if (cm.somethingSelected()) {
                cm.indentSelection("add");
            } else {
                cm.replaceSelection("  ", "end");
            }
        }}
    });

    // 配置已有规则编辑器
    existingRulesEditor = CodeMirror.fromTextArea(document.getElementById('existing-rules'), {
        mode: 'yaml',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true,
        styleActiveLine: true,
        indentUnit: 2,
        tabSize: 2,
        autofocus: false,
        scrollbarStyle: 'simple', // 使用SimpleScrollbars插件
        viewportMargin: 10, // 设置合理的视口边距
        extraKeys: {"Tab": function(cm) {
            if (cm.somethingSelected()) {
                cm.indentSelection("add");
            } else {
                cm.replaceSelection("  ", "end");
            }
        }}
    });

    // 添加编辑器内容变化时的自动保存功能
    // 使用防抖函数，避免频繁保存

    // 头部编辑器自动保存
    headerEditor.on('change', debounce(() => {
        saveAllConfig();
    }, 2000)); // 2秒后保存

    // 用户自定义规则编辑器自动保存
    userRulesEditor.on('change', debounce(() => {
        saveAllConfig();
    }, 2000)); // 2秒后保存

    // 已有规则编辑器自动保存
    existingRulesEditor.on('change', debounce(() => {
        saveAllConfig();
    }, 2000)); // 2秒后保存
}

// 初始化配置
function initLocalStorage() {
    // 这个函数现在只是为了兼容性保留，实际上我们从服务器加载配置
    console.log('正在从服务器加载配置...');
}

// 从服务器加载配置数据
async function loadFromLocalStorage() {
    try {
        showLoading(true);
        console.log('开始从服务器加载配置...');

        // 检查是否存在已生成的配置文件
        try {
            const configCheckResponse = await fetch('/api/check-config-exists');
            const configCheckData = await configCheckResponse.json();

            console.log('配置文件检查结果:', configCheckData);

            if (configCheckData.exists) {
                // 如果配置文件存在，显示配置链接
                document.getElementById('config-url').value = configCheckData.configUrl;

                // 显示上次修改时间
                if (configCheckData.lastModified) {
                    const lastModified = new Date(configCheckData.lastModified);
                    console.log(`配置文件最后修改时间: ${lastModified.toLocaleString()}`);
                    showStatus(`已找到现有配置文件 (上次修改: ${lastModified.toLocaleString()})`, 'info', 5000);

                    // 更新配置生成时间显示
                    if (configGenerationTime) {
                        const timeString = lastModified.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                        configGenerationTime.textContent = `生成时间: ${timeString}`;
                    }
                }
            } else {
                // 如果配置文件不存在，清空配置链接
                document.getElementById('config-url').value = '';
                console.log('未找到现有配置文件');
            }
        } catch (configCheckError) {
            console.error('检查配置文件失败:', configCheckError);
        }

        // 从服务器获取配置
        try {
            const response = await fetch('/api/load-config');
            console.log(`服务器响应状态: ${response.status}`);

            // 尝试解析响应
            let config;
            try {
                config = await response.json();
                console.log('服务器响应数据:', config);

                if (config.error) {
                    console.warn('服务器返回错误:', config.error);
                    showStatus(`加载配置警告: ${config.error}`, 'warning');
                }

                if (config.message) {
                    console.log('服务器消息:', config.message);
                }

                // 加载订阅
                const subscriptionList = document.getElementById('subscription-list');

                // 清空现有订阅行
                subscriptionList.innerHTML = '';

                if (!config.subscriptions || config.subscriptions.length === 0) {
                    console.log('没有找到订阅配置，添加默认空行');
                    // 如果没有保存的订阅，添加两个空行
                    addSubscriptionRow();
                    addSubscriptionRow();
                } else {
                    console.log(`加载 ${config.subscriptions.length} 个订阅`);
                    // 添加保存的订阅
                    config.subscriptions.forEach(sub => {
                        addSubscriptionRow(sub.url, sub.enabled);
                    });
                }

                // 加载头部配置和规则配置到CodeMirror编辑器
                console.log('设置编辑器内容...');
                headerEditor.setValue(config.header || '');
                userRulesEditor.setValue(config.userRules || '');
                existingRulesEditor.setValue(config.existingRules || '');

                // 加载自定义节点
                console.log('加载自定义节点...');
                loadCustomNodes(config.customNodes || []);

                // 显示上次更新时间
                if (config.lastUpdated) {
                    const lastUpdated = new Date(config.lastUpdated);
                    console.log(`配置最后更新时间: ${lastUpdated.toLocaleString()}`);
                    showStatus(`配置已加载 (上次更新: ${lastUpdated.toLocaleString()})`, 'success');
                } else {
                    console.log('没有找到上次更新时间，使用默认配置');
                    showStatus('使用默认配置', 'info');
                }

                // 保存一次配置，确保配置文件存在
                if (!config.lastUpdated) {
                    console.log('首次加载，保存默认配置到服务器');
                    setTimeout(() => {
                        saveAllConfig().then(success => {
                            if (success) {
                                console.log('默认配置已保存到服务器');
                            }
                        });
                    }, 2000);
                }
            } catch (jsonError) {
                console.error('解析响应JSON失败:', jsonError);
                const responseText = await response.text();
                console.log('原始响应内容:', responseText);
                throw new Error(`解析服务器响应失败: ${jsonError.message}`);
            }

            if (!response.ok && !config.subscriptions) {
                throw new Error(`服务器错误: ${response.status}`);
            }
        } catch (fetchError) {
            console.error('获取配置失败:', fetchError);
            showStatus(`加载配置失败: ${fetchError.message}`, 'danger');

            // 如果加载失败，添加两个空行
            const subscriptionList = document.getElementById('subscription-list');
            subscriptionList.innerHTML = '';
            addSubscriptionRow();
            addSubscriptionRow();

            // 设置空编辑器内容
            headerEditor.setValue('');
            userRulesEditor.setValue('');
            existingRulesEditor.setValue('');
        }
    } catch (error) {
        console.error('加载配置失败:', error);
        showStatus(`加载配置失败: ${error.message}`, 'danger');

        // 如果加载失败，添加两个空行
        const subscriptionList = document.getElementById('subscription-list');
        subscriptionList.innerHTML = '';
        addSubscriptionRow();
        addSubscriptionRow();

        // 设置空编辑器内容
        headerEditor.setValue('');
        userRulesEditor.setValue('');
        existingRulesEditor.setValue('');
    } finally {
        showLoading(false);
    }
}

// 添加订阅行
function addSubscriptionRow(url = '', enabled = true) {
    const subscriptionList = document.getElementById('subscription-list');
    const row = document.createElement('div');
    row.className = 'subscription-row mb-3';

    row.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control subscription-url" placeholder="Clash订阅链接" value="${url}">
            <div class="input-group-text">
                <div class="form-check form-switch">
                    <input class="form-check-input subscription-enabled" type="checkbox" ${enabled ? 'checked' : ''}>
                    <label class="form-check-label">启用</label>
                </div>
            </div>
            <button class="btn btn-outline-danger remove-subscription" type="button">删除</button>
        </div>
    `;

    subscriptionList.appendChild(row);
}

// 保存所有配置到服务器
async function saveAllConfig() {
    try {
        const subscriptions = getAllSubscriptions();
        const header = headerEditor.getValue();
        const userRules = userRulesEditor.getValue();
        const existingRules = existingRulesEditor.getValue();
        const customNodes = getAllCustomNodes();

        console.log('准备保存配置到服务器...');
        console.log(`订阅数量: ${subscriptions.length}`);
        console.log(`头部配置长度: ${header.length} 字符`);
        console.log(`用户规则长度: ${userRules.length} 字符`);
        console.log(`已有规则长度: ${existingRules.length} 字符`);
        console.log(`自定义节点数量: ${customNodes.length}`);

        // 发送到服务器
        try {
            const response = await fetch('/api/save-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    subscriptions,
                    header,
                    userRules,
                    existingRules,
                    customNodes
                })
            });

            console.log(`服务器响应状态: ${response.status}`);

            // 尝试解析响应
            let responseData;
            try {
                responseData = await response.json();
                console.log('服务器响应数据:', responseData);
            } catch (jsonError) {
                console.error('解析响应JSON失败:', jsonError);
                const responseText = await response.text();
                console.log('原始响应内容:', responseText);
                throw new Error(`解析服务器响应失败: ${jsonError.message}`);
            }

            if (!response.ok) {
                throw new Error(responseData.error || `服务器错误: ${response.status}`);
            }

            // 显示成功消息，但不要打断用户操作
            showStatus('配置已自动保存', 'success');

            return true;
        } catch (fetchError) {
            console.error('发送请求失败:', fetchError);
            showStatus(`保存配置失败: ${fetchError.message}`, 'danger');
            return false;
        }
    } catch (error) {
        console.error('保存配置失败:', error);
        showStatus(`保存配置失败: ${error.message}`, 'danger');
        return false;
    }
}

// 获取所有订阅信息（不保存）
function getAllSubscriptions() {
    const subscriptionRows = document.querySelectorAll('.subscription-row');
    const subscriptions = [];

    subscriptionRows.forEach(row => {
        const url = row.querySelector('.subscription-url').value.trim();
        const enabled = row.querySelector('.subscription-enabled').checked;

        // 保存所有订阅，即使URL为空
        subscriptions.push({ url, enabled });
    });

    return subscriptions;
}

// 保存所有订阅到服务器（带防抖）
const saveSubscriptions = debounce(async () => {
    await saveAllConfig();
}, 1000);

// 获取当前启用的订阅
function getEnabledSubscriptions() {
    const subscriptions = getAllSubscriptions();

    // 返回启用的且URL不为空的订阅URL
    return subscriptions.filter(sub => sub.enabled && sub.url).map(sub => sub.url);
}

// 获取节点
async function fetchNodes() {
    const enabledSubscriptions = getEnabledSubscriptions();

    if (enabledSubscriptions.length === 0) {
        showStatus('没有启用的订阅', 'warning');
        return;
    }

    showLoading(true);

    try {
        const response = await fetch('/api/fetch-nodes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ urls: enabledSubscriptions })
        });

        const data = await response.json();

        if (response.ok) {
            // 显示节点获取结果
            displayNodesFetchResult(data.results);
        } else {
            showStatus(`获取节点失败: ${data.error}`, 'danger');
        }
    } catch (error) {
        console.error('获取节点错误:', error);
        showStatus(`获取节点错误: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
    }
}

// 显示节点获取结果
function displayNodesFetchResult(results) {
    const nodesResult = document.getElementById('nodes-result');
    const nodesResultContent = document.getElementById('nodes-result-content');

    let htmlContent = '';
    let totalSuccess = 0;
    let totalFailed = 0;

    results.forEach(result => {
        if (result.success) {
            totalSuccess++;
            htmlContent += `<p>✅ 成功获取订阅 [${result.url}]: ${result.proxies.length} 个节点</p>`;
        } else {
            totalFailed++;
            htmlContent += `<p>❌ 获取订阅失败 [${result.url}]: ${result.error}</p>`;
        }
    });

    htmlContent += `<p><strong>总结:</strong> 成功 ${totalSuccess} 个订阅, 失败 ${totalFailed} 个订阅</p>`;

    nodesResultContent.innerHTML = htmlContent;
    nodesResult.classList.remove('d-none');

    if (totalSuccess > 0) {
        showStatus(`成功获取 ${totalSuccess} 个订阅的节点`, 'success');
    } else {
        showStatus('所有订阅获取失败', 'danger');
    }
}

// 生成配置
async function generateConfig() {
    const enabledSubscriptions = getEnabledSubscriptions();

    if (enabledSubscriptions.length === 0) {
        showStatus('没有启用的订阅', 'warning');
        return;
    }

    // 从CodeMirror编辑器获取当前内容并保存到服务器
    const header = headerEditor.getValue();
    const userRulesContent = userRulesEditor.getValue();
    const existingRulesContent = existingRulesEditor.getValue();
    const customNodes = getAllCustomNodes();

    // 保存所有配置到服务器
    await saveAllConfig();

    const rules = {
        userCustomRules: userRulesContent,
        existingRules: existingRulesContent
    };

    showLoading(true);

    try {
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                header,
                rules,
                enabledSubscriptions,
                customNodes
            })
        });

        // 检查HTTP状态
        if (!response.ok) {
            // 尝试解析错误消息
            let errorMessage;
            try {
                const contentType = response.headers.get("content-type");
                if (contentType && contentType.indexOf("application/json") !== -1) {
                    const errorData = await response.json();
                    errorMessage = errorData.error || `服务器错误: ${response.status}`;
                } else {
                    // 非JSON响应
                    const text = await response.text();
                    errorMessage = `服务器错误 (${response.status}): 非JSON响应`;
                    console.error('服务器返回非JSON响应:', text);
                }
            } catch (parseError) {
                errorMessage = `服务器错误 (${response.status}): 无法解析响应`;
                console.error('解析响应错误:', parseError);
            }

            showStatus(`生成配置失败: ${errorMessage}`, 'danger');
            showLoading(false);
            return;
        }

        // 正常解析JSON响应
        let data;
        try {
            data = await response.json();
        } catch (jsonError) {
            console.error('解析JSON响应错误:', jsonError);
            showStatus('生成配置失败: 解析服务器响应出错', 'danger');
            showLoading(false);
            return;
        }

        // 显示配置链接
        const configUrl = `${window.location.origin}/api/config/${data.configPath}`;
        document.getElementById('config-url').value = configUrl;
        document.getElementById('config-url-container').classList.remove('d-none');

        // 更新配置生成时间
        updateConfigGenerationTime();

        showStatus('配置文件生成成功', 'success');
    } catch (error) {
        console.error('生成配置错误:', error);
        showStatus(`生成配置错误: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    // 创建一个临时的textarea元素
    const textarea = document.createElement('textarea');
    textarea.value = text;

    // 设置样式使其不可见
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';

    // 添加到DOM
    document.body.appendChild(textarea);

    // 选择文本
    textarea.select();
    textarea.setSelectionRange(0, 99999); // 兼容移动设备

    try {
        // 执行复制命令
        const successful = document.execCommand('copy');
        if (successful) {
            showStatus('已复制到剪贴板', 'success');
        } else {
            throw new Error('复制命令执行失败');
        }
    } catch (err) {
        console.error('复制失败:', err);

        // 如果execCommand失败，尝试使用Clipboard API
        try {
            navigator.clipboard.writeText(text)
                .then(() => {
                    showStatus('已复制到剪贴板', 'success');
                })
                .catch(clipErr => {
                    console.error('Clipboard API失败:', clipErr);
                    showStatus('复制失败，请手动复制', 'warning');

                    // 选中输入框中的文本，方便用户手动复制
                    document.getElementById('config-url').focus();
                    document.getElementById('config-url').select();
                });
        } catch (clipErr) {
            console.error('Clipboard API不可用:', clipErr);
            showStatus('复制失败，请手动复制', 'warning');

            // 选中输入框中的文本，方便用户手动复制
            document.getElementById('config-url').focus();
            document.getElementById('config-url').select();
        }
    } finally {
        // 清理
        document.body.removeChild(textarea);
    }
}

// 显示/隐藏加载中遮罩
function showLoading(show) {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (show) {
        loadingOverlay.classList.remove('d-none');
    } else {
        loadingOverlay.classList.add('d-none');
    }
}

// 显示状态消息
function showStatus(message, type = 'info', duration = 3000) {
    const statusContainer = document.getElementById('status-container');
    const statusMessage = document.getElementById('status-message');
    const alertElement = statusContainer.querySelector('.alert');

    // 设置消息
    statusMessage.textContent = message;

    // 设置类型
    alertElement.className = `alert alert-${type}`;

    // 显示容器
    statusContainer.classList.remove('d-none');

    // 指定时间后自动隐藏
    setTimeout(() => {
        statusContainer.classList.add('d-none');
    }, duration);
}

// 初始化文件系统检查功能
document.addEventListener('DOMContentLoaded', () => {
    // 事件监听器 - 检查文件系统
    const checkFilesystemBtn = document.getElementById('check-filesystem-btn');
    const filesystemResult = document.getElementById('filesystem-result');
    const filesystemResultContent = document.getElementById('filesystem-result-content');

    if (checkFilesystemBtn) {
        checkFilesystemBtn.addEventListener('click', async () => {
            try {
                showLoading(true);
                checkFilesystemBtn.disabled = true;

                const response = await fetch('/api/check-filesystem');
                const data = await response.json();

                // 显示结果
                filesystemResult.classList.remove('d-none');

                // 格式化结果
                let resultHtml = '';

                // 添加摘要
                if (data.summary) {
                    const alertClass = data.summary.success ? 'alert-success' : 'alert-danger';
                    resultHtml += `<div class="alert ${alertClass} p-2 mb-2">${data.summary.message}</div>`;
                }

                // 添加检查结果
                if (data.checks && data.checks.length > 0) {
                    resultHtml += '<table class="table table-sm table-bordered">';
                    resultHtml += '<thead><tr><th>检查项</th><th>结果</th><th>路径</th><th>消息</th></tr></thead>';
                    resultHtml += '<tbody>';

                    data.checks.forEach(check => {
                        const rowClass = check.success ? 'table-success' : 'table-danger';
                        const statusIcon = check.success ? '✅' : '❌';

                        resultHtml += `<tr class="${rowClass}">`;
                        resultHtml += `<td>${check.name}</td>`;
                        resultHtml += `<td>${statusIcon}</td>`;
                        resultHtml += `<td class="text-break"><small>${check.path}</small></td>`;
                        resultHtml += `<td>${check.message}</td>`;
                        resultHtml += '</tr>';
                    });

                    resultHtml += '</tbody></table>';
                }

                // 添加进程信息
                if (data.process) {
                    resultHtml += '<h6 class="mt-3">进程信息:</h6>';
                    resultHtml += '<table class="table table-sm">';
                    resultHtml += `<tr><td>PID</td><td>${data.process.pid}</td></tr>`;
                    resultHtml += `<tr><td>平台</td><td>${data.process.platform}</td></tr>`;
                    resultHtml += `<tr><td>Node版本</td><td>${data.process.version}</td></tr>`;
                    resultHtml += `<tr><td>工作目录</td><td>${data.process.cwd}</td></tr>`;

                    if (data.process.uid !== 'N/A') {
                        resultHtml += `<tr><td>UID</td><td>${data.process.uid}</td></tr>`;
                    }

                    if (data.process.gid !== 'N/A') {
                        resultHtml += `<tr><td>GID</td><td>${data.process.gid}</td></tr>`;
                    }

                    resultHtml += '</table>';
                }

                filesystemResultContent.innerHTML = resultHtml;

                // 显示状态
                if (data.summary && data.summary.success) {
                    showStatus('文件系统检查完成: 一切正常', 'success');
                } else {
                    showStatus('文件系统检查完成: 发现问题', 'warning');
                }
            } catch (error) {
                console.error('检查文件系统失败:', error);
                showStatus(`检查文件系统失败: ${error.message}`, 'danger');

                filesystemResult.classList.remove('d-none');
                filesystemResultContent.innerHTML = `<div class="alert alert-danger">检查失败: ${error.message}</div>`;
            } finally {
                showLoading(false);
                checkFilesystemBtn.disabled = false;
            }
        });
    }
});

// 初始化定时任务功能
function initAutoTaskFeature() {
    // 获取定时任务相关元素
    autoTaskEnabled = document.getElementById('auto-task-enabled');
    autoTaskTime = document.getElementById('auto-task-time');
    autoTaskStatus = document.getElementById('auto-task-status');
    nextExecutionTime = document.getElementById('next-execution-time');
    lastExecutionTime = document.getElementById('last-execution-time');
    saveAutoTaskBtn = document.getElementById('save-auto-task-btn');
    runTaskNowBtn = document.getElementById('run-task-now-btn');
    configGenerationTime = document.getElementById('config-generation-time');

    // 检查元素是否存在
    if (!autoTaskEnabled || !autoTaskTime || !autoTaskStatus || !nextExecutionTime ||
        !lastExecutionTime || !saveAutoTaskBtn || !runTaskNowBtn || !configGenerationTime) {
        console.error('定时任务相关元素未找到，请检查HTML结构');
        return;
    }

    // 绑定事件监听器
    saveAutoTaskBtn.addEventListener('click', saveAutoTaskConfig);
    runTaskNowBtn.addEventListener('click', runTaskNow);

    // 加载定时任务状态
    loadAutoTaskStatus();

    console.log('定时任务功能初始化完成');
}

// 定时任务相关函数
async function loadAutoTaskStatus() {
    try {
        // 检查元素是否存在
        if (!autoTaskEnabled || !autoTaskTime || !autoTaskStatus || !nextExecutionTime || !lastExecutionTime) {
            console.warn('定时任务相关元素未初始化，跳过状态加载');
            return;
        }

        const response = await fetch('/api/auto-task/status');
        const data = await response.json();

        // 更新UI
        autoTaskEnabled.checked = data.enabled;
        autoTaskTime.value = data.time;
        autoTaskStatus.textContent = data.enabled ? (data.isRunning ? '运行中' : '已启用') : '未启用';
        nextExecutionTime.textContent = data.nextExecutionFormatted || '-';
        lastExecutionTime.textContent = data.lastExecutionFormatted || '-';

        console.log('定时任务状态已加载:', data);
    } catch (error) {
        console.error('加载定时任务状态失败:', error);
        showStatus('加载定时任务状态失败', 'warning');
    }
}

async function saveAutoTaskConfig() {
    try {
        // 检查元素是否存在
        if (!autoTaskEnabled || !autoTaskTime || !saveAutoTaskBtn) {
            console.error('定时任务相关元素未初始化');
            showStatus('定时任务功能未初始化', 'danger');
            return;
        }

        showLoading(true);
        saveAutoTaskBtn.disabled = true;

        const enabled = autoTaskEnabled.checked;
        const time = autoTaskTime.value;

        if (!time) {
            showStatus('请设置执行时间', 'warning');
            return;
        }

        const response = await fetch('/api/auto-task/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enabled, time })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '保存失败');
        }

        showStatus(data.message, 'success');

        // 重新加载状态
        await loadAutoTaskStatus();

    } catch (error) {
        console.error('保存定时任务配置失败:', error);
        showStatus(`保存失败: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
        saveAutoTaskBtn.disabled = false;
    }
}

async function runTaskNow() {
    try {
        // 检查元素是否存在
        if (!runTaskNowBtn) {
            console.error('定时任务相关元素未初始化');
            showStatus('定时任务功能未初始化', 'danger');
            return;
        }

        showLoading(true);
        runTaskNowBtn.disabled = true;

        showStatus('正在执行定时任务...', 'info');

        const response = await fetch('/api/auto-task/run-now', {
            method: 'POST'
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '执行失败');
        }

        if (data.success) {
            showStatus('定时任务执行成功，配置已生成', 'success');

            // 重新检查配置文件
            const configCheckResponse = await fetch('/api/check-config-exists');
            const configCheckData = await configCheckResponse.json();

            if (configCheckData.exists) {
                document.getElementById('config-url').value = configCheckData.configUrl;
                updateConfigGenerationTime();
            }
        } else {
            showStatus(`执行失败: ${data.message}`, 'warning');
        }

        // 重新加载状态
        await loadAutoTaskStatus();

    } catch (error) {
        console.error('立即执行定时任务失败:', error);
        showStatus(`执行失败: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
        runTaskNowBtn.disabled = false;
    }
}

// 更新配置生成时间显示
function updateConfigGenerationTime() {
    try {
        // 检查元素是否存在
        if (!configGenerationTime) {
            console.warn('配置生成时间元素未初始化，跳过更新');
            return;
        }

        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        configGenerationTime.textContent = `生成时间: ${timeString}`;
    }

// 自定义节点相关函数

// 添加自定义节点
function addCustomNode(nodeType) {
    const customNodesList = document.getElementById('custom-nodes-list');
    const nodeCard = document.createElement('div');
    nodeCard.className = 'custom-node-card card mb-3';

    let nodeTemplate = '';
    let nodeTitle = '';
    let nodeIcon = '';

    switch (nodeType) {
        case 'trojan':
            nodeTitle = 'Trojan节点';
            nodeIcon = 'bi-shield-lock';
            nodeTemplate = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label class="form-label small">服务器地址</label>
                            <input type="text" class="form-control form-control-sm node-server" placeholder="example.com">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">端口</label>
                            <input type="number" class="form-control form-control-sm node-port" placeholder="443" min="1" max="65535">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">密码</label>
                            <input type="text" class="form-control form-control-sm node-password" placeholder="your-password">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label class="form-label small">SNI</label>
                            <input type="text" class="form-control form-control-sm node-sni" placeholder="example.com">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">跳过证书验证</label>
                            <select class="form-select form-select-sm node-skip-cert-verify">
                                <option value="false">否</option>
                                <option value="true">是</option>
                            </select>
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">网络类型</label>
                            <select class="form-select form-select-sm node-network">
                                <option value="tcp">TCP</option>
                                <option value="ws">WebSocket</option>
                            </select>
                        </div>
                    </div>
                </div>`;
            break;

        case 'ss':
            nodeTitle = 'Shadowsocks节点';
            nodeIcon = 'bi-key';
            nodeTemplate = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label class="form-label small">服务器地址</label>
                            <input type="text" class="form-control form-control-sm node-server" placeholder="example.com">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">端口</label>
                            <input type="number" class="form-control form-control-sm node-port" placeholder="8388" min="1" max="65535">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">密码</label>
                            <input type="text" class="form-control form-control-sm node-password" placeholder="your-password">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label class="form-label small">加密方式</label>
                            <select class="form-select form-select-sm node-cipher">
                                <option value="aes-256-gcm">aes-256-gcm</option>
                                <option value="aes-128-gcm">aes-128-gcm</option>
                                <option value="chacha20-poly1305">chacha20-poly1305</option>
                                <option value="chacha20-ietf-poly1305">chacha20-ietf-poly1305</option>
                            </select>
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">插件</label>
                            <input type="text" class="form-control form-control-sm node-plugin" placeholder="可选">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">插件选项</label>
                            <input type="text" class="form-control form-control-sm node-plugin-opts" placeholder="可选">
                        </div>
                    </div>
                </div>`;
            break;

        case 'vmess':
            nodeTitle = 'VMess节点';
            nodeIcon = 'bi-cloud';
            nodeTemplate = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label class="form-label small">服务器地址</label>
                            <input type="text" class="form-control form-control-sm node-server" placeholder="example.com">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">端口</label>
                            <input type="number" class="form-control form-control-sm node-port" placeholder="443" min="1" max="65535">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">UUID</label>
                            <input type="text" class="form-control form-control-sm node-uuid" placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">AlterID</label>
                            <input type="number" class="form-control form-control-sm node-alterId" placeholder="0" min="0" max="255">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label class="form-label small">加密方式</label>
                            <select class="form-select form-select-sm node-cipher">
                                <option value="auto">auto</option>
                                <option value="aes-128-gcm">aes-128-gcm</option>
                                <option value="chacha20-poly1305">chacha20-poly1305</option>
                                <option value="none">none</option>
                            </select>
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">网络类型</label>
                            <select class="form-select form-select-sm node-network">
                                <option value="tcp">TCP</option>
                                <option value="ws">WebSocket</option>
                                <option value="h2">HTTP/2</option>
                            </select>
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">TLS</label>
                            <select class="form-select form-select-sm node-tls">
                                <option value="false">否</option>
                                <option value="true">是</option>
                            </select>
                        </div>
                        <div class="form-group mb-2">
                            <label class="form-label small">WebSocket路径</label>
                            <input type="text" class="form-control form-control-sm node-ws-path" placeholder="/" disabled>
                        </div>
                    </div>
                </div>`;
            break;
    }

    nodeCard.innerHTML = `
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="bi ${nodeIcon}"></i> ${nodeTitle}
            </h6>
            <button type="button" class="btn btn-sm btn-outline-danger remove-custom-node">
                <i class="bi bi-trash"></i> 删除
            </button>
        </div>
        <div class="card-body">
            <input type="hidden" class="node-type" value="${nodeType}">
            ${nodeTemplate}
        </div>
    `;

    customNodesList.appendChild(nodeCard);

    // 添加网络类型变化监听器（仅对VMess）
    if (nodeType === 'vmess') {
        const networkSelect = nodeCard.querySelector('.node-network');
        const wsPathInput = nodeCard.querySelector('.node-ws-path');

        networkSelect.addEventListener('change', () => {
            wsPathInput.disabled = networkSelect.value !== 'ws';
        });
    }

    saveCustomNodes();
    updateCustomNodesResult();
}

// 获取所有自定义节点
function getAllCustomNodes() {
    const customNodeCards = document.querySelectorAll('.custom-node-card');
    const customNodes = [];

    customNodeCards.forEach(card => {
        const nodeType = card.querySelector('.node-type').value;
        const server = card.querySelector('.node-server').value.trim();
        const port = card.querySelector('.node-port').value.trim();

        if (!server || !port) return; // 跳过不完整的节点

        const baseNode = {
            type: nodeType,
            server: server,
            port: parseInt(port),
            udp: true
        };

        switch (nodeType) {
            case 'trojan':
                const trojanPassword = card.querySelector('.node-password').value.trim();
                const sni = card.querySelector('.node-sni').value.trim();
                const skipCertVerify = card.querySelector('.node-skip-cert-verify').value === 'true';
                const network = card.querySelector('.node-network').value;

                if (!trojanPassword) return;

                customNodes.push({
                    ...baseNode,
                    password: trojanPassword,
                    sni: sni || server,
                    'skip-cert-verify': skipCertVerify,
                    network: network
                });
                break;

            case 'ss':
                const ssPassword = card.querySelector('.node-password').value.trim();
                const cipher = card.querySelector('.node-cipher').value;
                const plugin = card.querySelector('.node-plugin').value.trim();
                const pluginOpts = card.querySelector('.node-plugin-opts').value.trim();

                if (!ssPassword) return;

                const ssNode = {
                    ...baseNode,
                    password: ssPassword,
                    cipher: cipher
                };

                if (plugin) {
                    ssNode.plugin = plugin;
                    if (pluginOpts) {
                        ssNode['plugin-opts'] = pluginOpts;
                    }
                }

                customNodes.push(ssNode);
                break;

            case 'vmess':
                const uuid = card.querySelector('.node-uuid').value.trim();
                const alterId = card.querySelector('.node-alterId').value.trim();
                const vmessCipher = card.querySelector('.node-cipher').value;
                const vmessNetwork = card.querySelector('.node-network').value;
                const tls = card.querySelector('.node-tls').value === 'true';
                const wsPath = card.querySelector('.node-ws-path').value.trim();

                if (!uuid) return;

                const vmessNode = {
                    ...baseNode,
                    uuid: uuid,
                    alterId: parseInt(alterId) || 0,
                    cipher: vmessCipher,
                    network: vmessNetwork,
                    tls: tls
                };

                if (vmessNetwork === 'ws' && wsPath) {
                    vmessNode['ws-opts'] = {
                        path: wsPath
                    };
                }

                customNodes.push(vmessNode);
                break;
        }
    });

    return customNodes;
}

// 加载自定义节点
function loadCustomNodes(customNodes) {
    const customNodesList = document.getElementById('custom-nodes-list');
    customNodesList.innerHTML = '';

    customNodes.forEach(node => {
        addCustomNodeFromData(node);
    });

    updateCustomNodesResult();
}

// 从数据添加自定义节点
function addCustomNodeFromData(nodeData) {
    const nodeType = nodeData.type;
    addCustomNode(nodeType);

    const customNodesList = document.getElementById('custom-nodes-list');
    const lastCard = customNodesList.lastElementChild;

    // 填充基本信息
    lastCard.querySelector('.node-server').value = nodeData.server || '';
    lastCard.querySelector('.node-port').value = nodeData.port || '';

    switch (nodeType) {
        case 'trojan':
            lastCard.querySelector('.node-password').value = nodeData.password || '';
            lastCard.querySelector('.node-sni').value = nodeData.sni || '';
            lastCard.querySelector('.node-skip-cert-verify').value = nodeData['skip-cert-verify'] ? 'true' : 'false';
            lastCard.querySelector('.node-network').value = nodeData.network || 'tcp';
            break;

        case 'ss':
            lastCard.querySelector('.node-password').value = nodeData.password || '';
            lastCard.querySelector('.node-cipher').value = nodeData.cipher || 'aes-256-gcm';
            lastCard.querySelector('.node-plugin').value = nodeData.plugin || '';
            lastCard.querySelector('.node-plugin-opts').value = nodeData['plugin-opts'] || '';
            break;

        case 'vmess':
            lastCard.querySelector('.node-uuid').value = nodeData.uuid || '';
            lastCard.querySelector('.node-alterId').value = nodeData.alterId || 0;
            lastCard.querySelector('.node-cipher').value = nodeData.cipher || 'auto';
            lastCard.querySelector('.node-network').value = nodeData.network || 'tcp';
            lastCard.querySelector('.node-tls').value = nodeData.tls ? 'true' : 'false';

            if (nodeData.network === 'ws' && nodeData['ws-opts'] && nodeData['ws-opts'].path) {
                lastCard.querySelector('.node-ws-path').value = nodeData['ws-opts'].path;
                lastCard.querySelector('.node-ws-path').disabled = false;
            }
            break;
    }
}

// 保存自定义节点（带防抖）
const saveCustomNodes = debounce(async () => {
    await saveAllConfig();
}, 1000);

// 更新自定义节点结果显示
function updateCustomNodesResult() {
    const customNodes = getAllCustomNodes();
    const customNodesResult = document.getElementById('custom-nodes-result');
    const customNodesResultContent = document.getElementById('custom-nodes-result-content');

    if (customNodes.length > 0) {
        const typeCount = {};
        customNodes.forEach(node => {
            typeCount[node.type] = (typeCount[node.type] || 0) + 1;
        });

        let htmlContent = `<p><strong>总计:</strong> ${customNodes.length} 个自定义节点</p>`;
        Object.entries(typeCount).forEach(([type, count]) => {
            const typeNames = {
                'trojan': 'Trojan',
                'ss': 'Shadowsocks',
                'vmess': 'VMess'
            };
            htmlContent += `<p>• ${typeNames[type] || type}: ${count} 个</p>`;
        });

        customNodesResultContent.innerHTML = htmlContent;
        customNodesResult.classList.remove('d-none');
    } else {
        customNodesResult.classList.add('d-none');
    }
} catch (error) {
        console.error('更新配置生成时间失败:', error);
    }
}